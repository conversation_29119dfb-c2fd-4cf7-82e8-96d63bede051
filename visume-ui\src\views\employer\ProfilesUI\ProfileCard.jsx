// ProfileCard.jsx
import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { HiBriefcase, HiHeart, HiLocationMarker, HiOutlineHeart, HiOutlineLocationMarker, HiVideoCamera, HiX } from "react-icons/hi";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const ProfileCard = ({
  keyValue,
  experience_range,
  shortListedProfiles,
  score,
  video_profile_id,
  candidateDetails,
  role,
  id,
  onShortlist,
  isShortlisted,
  isLoading,
  cand_id,
  skills,
  isSkeleton,
  onSkillsTooltipChange, // New prop to handle global tooltip state
  activeSkillsTooltip, // Global state to determine which tooltip is active
  onLocationTooltipChange, // New prop to handle global location tooltip state
  activeLocationTooltip // Global state to determine which location tooltip is active
}) => {
  const skillsArray = skills ? skills.split(",").map((skill) => skill.trim()) : [];
  const [imageError, setImageError] = useState(false);
  const skillsButtonRef = useRef(null);
  const tooltipRef = useRef(null);
  const locationButtonRef = useRef(null);
  const locationTooltipRef = useRef(null);
  const emp_id = Cookies.get("employerId");
  const [hasViewed, setHasViewed] = useState(false);
  const [hasClicked, setHasClicked] = useState(false);

  // Determine if this card's tooltips should be shown based on global state
  const showSkillsTooltip = activeSkillsTooltip === video_profile_id;
  const showLocationTooltip = activeLocationTooltip === video_profile_id;

  const navigate = useNavigate();

  // Handle click outside to close tooltips
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Handle skills tooltip
      if (
        showSkillsTooltip &&
        skillsButtonRef.current &&
        tooltipRef.current &&
        !skillsButtonRef.current.contains(event.target) &&
        !tooltipRef.current.contains(event.target)
      ) {
        if (onSkillsTooltipChange) {
          onSkillsTooltipChange(null);
        }
      }

      // Handle location tooltip
      if (
        showLocationTooltip &&
        locationButtonRef.current &&
        locationTooltipRef.current &&
        !locationButtonRef.current.contains(event.target) &&
        !locationTooltipRef.current.contains(event.target)
      ) {
        if (onLocationTooltipChange) {
          onLocationTooltipChange(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSkillsTooltip, showLocationTooltip, onSkillsTooltipChange, onLocationTooltipChange]);

  // Handle skills tooltip toggle
  const handleSkillsClick = (e) => {
    e.stopPropagation();

    // Notify parent component about tooltip state change
    if (onSkillsTooltipChange) {
      // If this tooltip is currently active, close it; otherwise, open it
      onSkillsTooltipChange(showSkillsTooltip ? null : video_profile_id);
    }
  };

  // Handle location tooltip toggle
  const handleLocationClick = (e) => {
    e.stopPropagation();

    // Notify parent component about tooltip state change
    if (onLocationTooltipChange) {
      // If this tooltip is currently active, close it; otherwise, open it
      onLocationTooltipChange(showLocationTooltip ? null : video_profile_id);
    }
  };

  // Calculate tooltip position to prevent off-screen issues
  const getTooltipPosition = (buttonRef, isLocationTooltip = false) => {
    if (!buttonRef.current) return { left: '0', top: '0' };

    const buttonRect = buttonRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const tooltipWidth = 320; // 80 * 4 (w-80)
    const tooltipHeight = isLocationTooltip ? 200 : 300; // location tooltip is smaller

    let left = buttonRect.left;
    let top = buttonRect.bottom + 8; // 8px gap below button

    // Adjust horizontal position if tooltip would go off-screen
    if (left + tooltipWidth > viewportWidth - 20) {
      left = viewportWidth - tooltipWidth - 20;
    }
    if (left < 20) {
      left = 20;
    }

    // Adjust vertical position if tooltip would go off-screen
    if (top + tooltipHeight > viewportHeight - 20) {
      top = buttonRect.top - tooltipHeight - 8; // Position above button
    }
    if (top < 20) {
      top = 20;
    }

    return { left: `${left}px`, top: `${top}px` };
  };

  const profileClick = async () => {
    const currentUrl = window.location.href;
    localStorage.setItem("previousUrl", currentUrl);
    navigate(`/profile/${video_profile_id}`);
    if (!hasClicked) {
      try {
        if (!emp_id) {
          return toast.error("You need to be an employer to shortlist profiles");
        }
        const response = await fetch(
          `${import.meta.env.VITE_APP_HOST}/api/v1/candidate/analytics`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              employer_id: emp_id,
              profile_id: id,
              interaction_type: "click",
            }),
          }
        );
        if (!response.ok) {
          const msg = await response.json();
          if (
            msg.message ===
            "Duplicate interaction: the same interaction type already exists for this employer and profile."
          ) {
            toast(msg.message);
            return;
          } else {
            toast.error(msg.message);
            throw new Error(`HTTP error! status: ${msg.message}`);
          }
        }
        await response.json();
      } catch (err) {
        console.log(err);
      } finally {
        setHasClicked(true);
      }
    }
  };

  return (
    <div
      key={keyValue}
      className={`relative flex flex-col rounded-2xl ${
        isSkeleton
          ? 'bg-gray-50 animate-pulse'
          : 'bg-gradient-to-br from-white via-gray-50 to-gray-100'
      } border border-gray-200 shadow-xl p-5 transition-all duration-200 hover:shadow-2xl overflow-hidden`}
    >
      {/* Header Row: Profile Image, Name, Role */}
      <div className="flex items-start gap-4 mb-4">
        {/* Profile Image */}
        <div
          className={`h-16 w-16 flex-shrink-0 ${!isSkeleton && 'cursor-pointer'} rounded-full border-4 border-white shadow-md ${isSkeleton ? 'bg-gray-300' : 'bg-gray-200'} overflow-hidden`}
          onClick={!isSkeleton ? profileClick : undefined}
        >
          {imageError ? (
            <div className="flex h-full w-full items-center justify-center rounded-full bg-brand-600 text-2xl font-semibold text-white">
              {candidateDetails[0].cand_name[0].toUpperCase()}
            </div>
          ) : (
            <img
              className="h-full w-full rounded-full object-cover"
              src={`${import.meta.env.VITE_APP_HOST}/${candidateDetails[0].profile_picture}`}
              alt={candidateDetails[0].cand_name}
              onError={() => setImageError(true)}
            />
          )}
        </div>

        {/* Name, Role, and Score */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0">
              <h2
                onClick={profileClick}
                className="text-lg font-bold text-gray-900 cursor-pointer hover:text-indigo-600 transition-colors duration-200 flex items-center gap-1 truncate"
              >
                <span className="truncate">{candidateDetails[0].cand_name}</span>
                <span className="text-indigo-500 flex-shrink-0">
                  <HiBriefcase />
                </span>
              </h2>
            </div>
            {/* Score Badge - Fixed positioning */}
            <div className="flex-shrink-0 ml-2">
              <span className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-purple-200 to-indigo-200 text-sm font-bold text-indigo-700 shadow">
                {Math.round(JSON.parse(score)?.score?.Overall_Score) || 0}
              </span>
            </div>
          </div>

          {/* Role and Experience Tags */}
          <div className="flex flex-wrap gap-2 mb-2">
            <span className="inline-flex items-center gap-1 rounded-full bg-purple-100 px-3 py-1 text-xs font-semibold text-purple-800 border border-purple-200">
              {role}
            </span>
            <span className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1 text-xs font-semibold text-blue-700 border border-blue-200">
              <span className="text-blue-400">
                <HiBriefcase />
              </span>
              Exp: {experience_range}
            </span>
          </div>

          {/* Location and Salary - Fixed overflow */}
          <div className="flex items-center text-xs text-gray-500 gap-2">
            <span className="flex items-center gap-1 flex-shrink-0">
              <span className="font-semibold text-green-600">₹12-14 LPA</span>
            </span>
            <span className="mx-1 flex-shrink-0">&#183;</span>
            <div className="relative flex items-center gap-1 min-w-0">
              <button
                ref={locationButtonRef}
                onClick={handleLocationClick}
                className="flex items-center gap-1 min-w-0 hover:bg-indigo-50 rounded px-1 py-0.5 transition-colors duration-200 cursor-pointer"
              >
                <HiOutlineLocationMarker className="text-indigo-400 flex-shrink-0" />
                <span className="truncate">
                  {candidateDetails[0].preferred_location.startsWith("[")
                    ? JSON.parse(candidateDetails[0].preferred_location).join(", ")
                    : candidateDetails[0].preferred_location}
                </span>
              </button>

              {/* Location Tooltip */}
              {showLocationTooltip && (
                <div
                  ref={locationTooltipRef}
                  className="fixed z-[9999] w-80 max-w-sm bg-white border border-gray-200 rounded-xl shadow-2xl p-4 transition-all duration-200 transform"
                  style={{
                    animation: 'fadeInUp 0.2s ease-out',
                    maxHeight: '200px',
                    overflowY: 'auto',
                    ...getTooltipPosition(locationButtonRef, true)
                  }}
                >
                  {/* Tooltip Header */}
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-900">Location Details</h3>
                    <button
                      onClick={() => onLocationTooltipChange && onLocationTooltipChange(null)}
                      className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    >
                      <HiX className="w-4 h-4 text-gray-500" />
                    </button>
                  </div>

                  {/* Location Content */}
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <HiOutlineLocationMarker className="text-indigo-400 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Preferred Location</p>
                        <p className="text-sm text-gray-600">
                          {candidateDetails[0].preferred_location.startsWith("[")
                            ? JSON.parse(candidateDetails[0].preferred_location).join(", ")
                            : candidateDetails[0].preferred_location}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Tooltip Arrow */}
                  <div className="absolute -top-2 left-4 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      {/* Skills Section with Tooltip */}
      <div className="relative">
        <button
          ref={skillsButtonRef}
          onClick={handleSkillsClick}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-gradient-to-r from-indigo-50 to-purple-50 hover:from-indigo-100 hover:to-purple-100 border border-indigo-200 transition-all duration-200 text-indigo-700 font-semibold text-sm"
        >
          <span>Skills</span>
          <span className="text-xs bg-indigo-200 text-indigo-800 px-2 py-0.5 rounded-full">
            {skillsArray.length}
          </span>
        </button>

        {/* Skills Tooltip */}
        {showSkillsTooltip && (
          <div
            ref={tooltipRef}
            className="fixed z-[9999] w-80 max-w-sm bg-white border border-gray-200 rounded-xl shadow-2xl p-4 transition-all duration-200 transform"
            style={{
              animation: 'fadeInUp 0.2s ease-out',
              maxHeight: '300px',
              overflowY: 'auto',
              ...getTooltipPosition(skillsButtonRef, false)
            }}
          >
            {/* Tooltip Header */}
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-gray-900">All Skills</h3>
              <button
                onClick={() => onSkillsTooltipChange && onSkillsTooltipChange(null)}
                className="p-1 rounded-full hover:bg-gray-100 transition-colors duration-200"
              >
                <HiX className="w-4 h-4 text-gray-500" />
              </button>
            </div>

            {/* Skills Grid */}
            <div className="flex flex-wrap gap-2">
              {skillsArray.map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 px-3 py-1 text-xs font-semibold text-indigo-700 border border-indigo-200 shadow-sm"
                >
                  <svg className="w-2 h-2 text-indigo-400" fill="currentColor" viewBox="0 0 20 20">
                    <circle cx="10" cy="10" r="10" />
                  </svg>
                  {skill}
                </span>
              ))}
            </div>

            {/* Tooltip Arrow */}
            <div className="absolute -top-2 left-4 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
          </div>
        )}
      </div>
      {/* Action Buttons */}
      {/* Match Details */}
      <div className="mt-4 flex items-center gap-2 text-xs">
        {JSON.parse(score)?.score?.Experience_Match > 70 && (
          <span className="inline-flex items-center gap-1 rounded-full bg-yellow-100 px-2 py-0.5 font-medium text-yellow-700">
            Experience Match
          </span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="mt-4 flex gap-3 border-t border-gray-200 pt-4">
        <button
          onClick={!isSkeleton ? profileClick : undefined}
          className={`flex flex-1 items-center justify-center gap-2 rounded-lg border ${
            isSkeleton
              ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-default'
              : 'border-indigo-200 bg-white text-indigo-700 hover:bg-indigo-50 hover:text-indigo-900'
          } px-3 py-2 text-sm font-semibold shadow-sm transition-colors`}
          disabled={isSkeleton}
        >
          <HiVideoCamera className="text-lg" />
          View Profile
        </button>
        <button
          className={`flex flex-1 items-center justify-center gap-2 rounded-lg px-3 py-2 text-sm font-semibold shadow-sm transition-colors ${
            isShortlisted || shortListedProfiles?.includes(video_profile_id)
              ? "bg-indigo-500 text-white"
              : "border border-gray-300 bg-white text-gray-700 hover:border-red-400 hover:text-red-500"
          }`}
          onClick={() =>
            !isShortlisted &&
            !shortListedProfiles?.includes(video_profile_id) &&
            onShortlist(id, cand_id)
          }
          disabled={isLoading}
        >
          {isLoading ? (
            <svg
              className="text-current h-4 w-4 animate-spin"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
                fill="none"
              />
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
          ) : isShortlisted ||
            shortListedProfiles?.includes(video_profile_id) ? (
            <>
              <HiHeart className="fill-current text-base" />
              Shortlisted
            </>
          ) : (
            <>
              <HiOutlineHeart className="text-base" />
              Shortlist
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ProfileCard;